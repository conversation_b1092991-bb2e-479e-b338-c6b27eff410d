import { deepStrictEqual as assertEquals } from 'node:assert';
import { test } from 'node:test';
import { heuristicChronologicalSort } from './syncUtils';

const records = [
  { id: 1, updatedAt: '2024-01-01T00:00:00Z' },
  { id: 2, updatedAt: '2024-02-01T00:00:00Z' },
  { id: 3, updatedAt: '2023-12-31T00:00:00Z' },
];

test('heuristicChronologicalSort orders by latest date first', () => {
  const sorted = heuristicChronologicalSort(records);
  assertEquals(sorted[0].id, 2);
  assertEquals(sorted[1].id, 1);
  assertEquals(sorted[2].id, 3);
});

