// Simplified schemas page to isolate the issue
import { useState } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';

export default function SchemasSimplePage() {
  const [schema, setSchema] = useState<any>(null);
  const router = useRouter();

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Inspector</span>
            </button>
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Schema Testing Tools (Simple)</span>
          </h1>
          <p className="text-muted-foreground">
            Test taskflow schemas, evaluate node parameters, and match conditions
          </p>
        </header>

        <div className="space-y-4">
          <p>This is a simplified version without the problematic components.</p>
          <p>If this loads, the issue is with one of the imported components.</p>
        </div>
      </div>
    </div>
  );
}
