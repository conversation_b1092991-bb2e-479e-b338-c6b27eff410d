// Test just the ConditionMatcher component
import { useState } from 'react';
import { Activity } from 'lucide-react';
import { ConditionMatcher } from '@/components/ConditionMatcher';

export default function SchemasTestConditionPage() {
  const [schema, setSchema] = useState<any>(null);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
          <Activity className="h-8 w-8" />
          <span>Testing ConditionMatcher</span>
        </h1>

        {/* Condition Matcher */}
        <ConditionMatcher schema={schema} />

        <div className="space-y-4">
          <p>This version tests only the ConditionMatcher component.</p>
        </div>
      </div>
    </div>
  );
}
