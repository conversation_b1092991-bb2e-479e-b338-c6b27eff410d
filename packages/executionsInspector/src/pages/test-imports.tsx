// Test individual imports to find the problematic one
import { useState } from 'react';

// Test each import one by one
try {
  // Test 1: Basic React and icons
  const { Activity, ArrowLeft } = require('lucide-react');
  console.log('✅ lucide-react icons imported successfully');
} catch (e) {
  console.error('❌ lucide-react icons failed:', e);
}

try {
  // Test 2: Next.js router
  const { useRouter } = require('next/router');
  console.log('✅ next/router imported successfully');
} catch (e) {
  console.error('❌ next/router failed:', e);
}

try {
  // Test 3: ThemeToggle
  const { ThemeToggle } = require('@/components/ThemeToggle');
  console.log('✅ ThemeToggle imported successfully', ThemeToggle);
} catch (e) {
  console.error('❌ ThemeToggle failed:', e);
}

try {
  // Test 4: SchemaInput
  const { SchemaInput } = require('@/components/SchemaInput');
  console.log('✅ SchemaInput imported successfully', SchemaInput);
} catch (e) {
  console.error('❌ SchemaInput failed:', e);
}

try {
  // Test 5: NodeParameterEvaluator
  const { NodeParameterEvaluator } = require('@/components/NodeParameterEvaluator');
  console.log('✅ NodeParameterEvaluator imported successfully', NodeParameterEvaluator);
} catch (e) {
  console.error('❌ NodeParameterEvaluator failed:', e);
}

try {
  // Test 6: ConditionMatcher
  const { ConditionMatcher } = require('@/components/ConditionMatcher');
  console.log('✅ ConditionMatcher imported successfully', ConditionMatcher);
} catch (e) {
  console.error('❌ ConditionMatcher failed:', e);
}

export default function TestImportsPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <h1>Testing Individual Imports</h1>
        <p>Check the console for import test results.</p>
      </div>
    </div>
  );
}
