// Test page to gradually add components to isolate the error
import { useState } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';

export default function TestGradualPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header - Testing basic icons and layout */}
        <header className="relative text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Schema Testing Tools</span>
          </h1>
          <p className="text-muted-foreground">
            Test taskflow schemas, evaluate node parameters, and match conditions
          </p>
        </header>

        <div>
          <p>Basic layout with icons works fine.</p>
        </div>
      </div>
    </div>
  );
}
