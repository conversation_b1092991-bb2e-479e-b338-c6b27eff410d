// Test just the imports for NodeParameterEvaluator
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Play, AlertCircle } from 'lucide-react';
import { evaluateNodeParameters } from '../lib/jsonLogicUtils';

export default function TestNodeImportsPage() {
  // Log all imports to see if any are undefined
  console.log('Card:', Card);
  console.log('CardContent:', CardContent);
  console.log('CardHeader:', CardHeader);
  console.log('CardTitle:', CardTitle);
  console.log('Button:', Button);
  console.log('Textarea:', Textarea);
  console.log('Select:', Select);
  console.log('SelectContent:', SelectContent);
  console.log('SelectItem:', SelectItem);
  console.log('SelectTrigger:', SelectTrigger);
  console.log('SelectValue:', SelectValue);
  console.log('Settings icon:', Settings);
  console.log('Play icon:', Play);
  console.log('AlertCircle icon:', AlertCircle);
  console.log('evaluateNodeParameters:', evaluateNodeParameters);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <h1>Testing NodeParameterEvaluator Imports</h1>
        <p>Check console for import values.</p>
        <p>All imports should be defined (not undefined).</p>
      </div>
    </div>
  );
}
