// Add SchemaInput to test it
import { useState } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import { SchemaInput } from '@/components/SchemaInput';
import { useRouter } from 'next/router';

export default function SchemasWithInputPage() {
  const [schema, setSchema] = useState<any>(null);
  const router = useRouter();

  const handleSchemaChange = (newSchema: any) => {
    setSchema(newSchema);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Inspector</span>
            </button>
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Schema Testing Tools (With Input)</span>
          </h1>
          <p className="text-muted-foreground">
            Test taskflow schemas, evaluate node parameters, and match conditions
          </p>
        </header>

        {/* Schema Input Section */}
        <SchemaInput onSchemaChange={handleSchemaChange} />

        <div className="space-y-4">
          <p>This version includes ThemeToggle and SchemaInput components.</p>
          <p>If this loads, both components are working correctly.</p>
        </div>
      </div>
    </div>
  );
}
