// Replicate the exact import structure from schemas.tsx to identify the undefined component
import { useState } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import { SchemaInput } from '@/components/SchemaInput';
import { NodeParameterEvaluator } from '@/components/NodeParameterEvaluator';
import { ConditionMatcher } from '@/components/ConditionMatcher';
import { useRouter } from 'next/router';

export default function ComponentTestPage() {
  const [schema, setSchema] = useState<any>(null);
  const router = useRouter();

  // Log each component to identify which ones are undefined
  console.log('=== Component Debug Info ===');
  console.log('Activity:', Activity);
  console.log('ArrowLeft:', ArrowLeft);
  console.log('ThemeToggle:', ThemeToggle);
  console.log('SchemaInput:', SchemaInput);
  console.log('NodeParameterEvaluator:', NodeParameterEvaluator);
  console.log('ConditionMatcher:', ConditionMatcher);
  console.log('useRouter:', useRouter);

  const handleSchemaChange = (newSchema: any) => {
    setSchema(newSchema);
  };

  // Check if any component is undefined before rendering
  const undefinedComponents = [];
  if (Activity === undefined) undefinedComponents.push('Activity');
  if (ArrowLeft === undefined) undefinedComponents.push('ArrowLeft');
  if (ThemeToggle === undefined) undefinedComponents.push('ThemeToggle');
  if (SchemaInput === undefined) undefinedComponents.push('SchemaInput');
  if (NodeParameterEvaluator === undefined) undefinedComponents.push('NodeParameterEvaluator');
  if (ConditionMatcher === undefined) undefinedComponents.push('ConditionMatcher');

  if (undefinedComponents.length > 0) {
    return (
      <div className="min-h-screen bg-red-50 p-6">
        <h1 className="text-2xl font-bold text-red-800 mb-4">❌ Undefined Components Detected</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">The following components are undefined:</p>
          <ul className="list-disc list-inside mt-2">
            {undefinedComponents.map(name => (
              <li key={name}>{name}</li>
            ))}
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Inspector</span>
            </button>
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Component Test Page</span>
          </h1>
          <p className="text-muted-foreground">
            All components loaded successfully!
          </p>
        </header>

        {/* Schema Input Section */}
        <SchemaInput onSchemaChange={handleSchemaChange} />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Node Parameter Evaluator */}
          <NodeParameterEvaluator schema={schema} />

          {/* Right Column - Condition Matcher */}
          <ConditionMatcher schema={schema} />
        </div>
      </div>
    </div>
  );
}
