// Test page to test just the SchemaInput component
import { useState } from 'react';
import { SchemaInput } from '@/components/SchemaInput';

export default function TestSchemaInputPage() {
  const [schema, setSchema] = useState<any>(null);

  const handleSchemaChange = (newSchema: any) => {
    setSchema(newSchema);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        <h1>Testing SchemaInput Component</h1>
        <SchemaInput onSchemaChange={handleSchemaChange} />
      </div>
    </div>
  );
}
