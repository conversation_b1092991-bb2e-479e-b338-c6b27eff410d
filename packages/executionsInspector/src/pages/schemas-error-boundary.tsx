// Final test version that replicates the exact structure with error boundaries
import React, { useState, ErrorInfo, ReactNode } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';

// Error boundary component
class ComponentErrorBoundary extends React.Component<
  { children: ReactNode; componentName: string },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; componentName: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`Error in ${this.props.componentName}:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-500 rounded bg-red-50 dark:bg-red-900/20">
          <h3 className="text-red-700 dark:text-red-300 font-semibold">
            Error in {this.props.componentName}
          </h3>
          <p className="text-red-600 dark:text-red-400 text-sm">
            {this.state.error?.message || 'Unknown error'}
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy load components to catch import errors
const ThemeToggle = React.lazy(() => 
  import('@/components/ThemeToggle').then(module => ({ default: module.ThemeToggle }))
);

const SchemaInput = React.lazy(() => 
  import('@/components/SchemaInput').then(module => ({ default: module.SchemaInput }))
);

const NodeParameterEvaluator = React.lazy(() => 
  import('@/components/NodeParameterEvaluator').then(module => ({ default: module.NodeParameterEvaluator }))
);

const ConditionMatcher = React.lazy(() => 
  import('@/components/ConditionMatcher').then(module => ({ default: module.ConditionMatcher }))
);

export default function SchemasErrorBoundaryPage() {
  const [schema, setSchema] = useState<any>(null);
  const router = useRouter();

  const handleSchemaChange = (newSchema: any) => {
    setSchema(newSchema);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Inspector</span>
            </button>
            <ComponentErrorBoundary componentName="ThemeToggle">
              <React.Suspense fallback={<div>Loading...</div>}>
                <ThemeToggle />
              </React.Suspense>
            </ComponentErrorBoundary>
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Schema Testing Tools (Error Boundary)</span>
          </h1>
          <p className="text-muted-foreground">
            Test taskflow schemas, evaluate node parameters, and match conditions
          </p>
        </header>

        {/* Schema Input Section */}
        <ComponentErrorBoundary componentName="SchemaInput">
          <React.Suspense fallback={<div>Loading SchemaInput...</div>}>
            <SchemaInput onSchemaChange={handleSchemaChange} />
          </React.Suspense>
        </ComponentErrorBoundary>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Node Parameter Evaluator */}
          <ComponentErrorBoundary componentName="NodeParameterEvaluator">
            <React.Suspense fallback={<div>Loading NodeParameterEvaluator...</div>}>
              <NodeParameterEvaluator schema={schema} />
            </React.Suspense>
          </ComponentErrorBoundary>

          {/* Right Column - Condition Matcher */}
          <ComponentErrorBoundary componentName="ConditionMatcher">
            <React.Suspense fallback={<div>Loading ConditionMatcher...</div>}>
              <ConditionMatcher schema={schema} />
            </React.Suspense>
          </ComponentErrorBoundary>
        </div>
      </div>
    </div>
  );
}
