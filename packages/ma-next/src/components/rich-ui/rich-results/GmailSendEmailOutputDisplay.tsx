import React from 'react';
import { Mail, Send, Tag, MoreHorizontal, ExternalLink, Paperclip } from 'lucide-react';
import { GmailSendEmailOutput, GmailAttachment } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GmailSendEmailOutputDisplayProps = {
  output: GmailSendEmailOutput;
  actionParameters?: Record<string, any>;
};

// Component for displaying email attachments
interface EmailAttachmentsProps {
  attachments: GmailAttachment[];
}

function EmailAttachments({ attachments }: EmailAttachmentsProps) {
  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 space-y-2">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Attachments ({attachments.length})
      </div>
      {attachments.map((attachment, index) => (
        <div
          key={index}
          className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
        >
          {/* File icon */}
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <Paperclip className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          {/* File info */}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {attachment.filename}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {attachment.mimeType}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Renders a rich display of a Gmail send email result
 */
function GmailSendEmailOutputDisplay({ output, actionParameters }: GmailSendEmailOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No email data available</p>
      </div>
    );
  }

  // Extract email info from action parameters
  const to = actionParameters?.to;
  const subject = actionParameters?.subject;
  const attachments = actionParameters?.attachments as GmailAttachment[] | undefined;

  const menuItems: ContextMenuItem[] = [
    {
      label: 'View in Gmail Sent Folder',
      href: 'https://mail.google.com/mail/u/0/#sent',
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Send className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Email Sent Successfully
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5 space-y-4">
        {/* Email details */}
        <div className="space-y-3">
          {subject && (
            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Subject
              </div>
              <div className="text-sm text-gray-900 dark:text-white">
                {subject}
              </div>
            </div>
          )}

          {to && (
            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                To
              </div>
              <div className="text-sm text-gray-900 dark:text-white">
                {to}
              </div>
            </div>
          )}
        </div>

        {/* Labels */}
        {/* {output.labelIds && output.labelIds.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Labels
            </div>
            <div className="flex flex-wrap gap-1.5">
              {output.labelIds.map((label, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {label.replace('CATEGORY_', '').toLowerCase()}
                </span>
              ))}
            </div>
          </div>
        )} */}

        {/* Attachments */}
        {attachments && attachments.length > 0 && (
          <EmailAttachments attachments={attachments} />
        )}

      </div>
    </div>
    </ContextMenu>
  );
}

export { GmailSendEmailOutputDisplay };
