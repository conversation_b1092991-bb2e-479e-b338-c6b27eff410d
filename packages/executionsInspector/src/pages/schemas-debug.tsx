// Debug version of schemas page to identify undefined components
import { useState } from 'react';
import { Activity, ArrowLeft } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import { SchemaInput } from '@/components/SchemaInput';
import { NodeParameterEvaluator } from '@/components/NodeParameterEvaluator';
import { ConditionMatcher } from '@/components/ConditionMatcher';
import { useRouter } from 'next/router';

export default function SchemasDebugPage() {
  const [schema, setSchema] = useState<any>(null);
  const router = useRouter();

  // Debug: Check if any components are undefined
  console.log('Component checks:');
  console.log('ThemeToggle:', ThemeToggle);
  console.log('SchemaInput:', SchemaInput);
  console.log('NodeParameterEvaluator:', NodeParameterEvaluator);
  console.log('ConditionMatcher:', ConditionMatcher);
  console.log('Activity icon:', Activity);
  console.log('ArrowLeft icon:', ArrowLeft);

  const handleSchemaChange = (newSchema: any) => {
    setSchema(newSchema);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Inspector</span>
            </button>
            {ThemeToggle && <ThemeToggle />}
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Schema Testing Tools (Debug)</span>
          </h1>
          <p className="text-muted-foreground">
            Test taskflow schemas, evaluate node parameters, and match conditions
          </p>
        </header>

        {/* Schema Input Section */}
        {SchemaInput && <SchemaInput onSchemaChange={handleSchemaChange} />}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Node Parameter Evaluator */}
          {NodeParameterEvaluator && <NodeParameterEvaluator schema={schema} />}

          {/* Right Column - Condition Matcher */}
          {ConditionMatcher && <ConditionMatcher schema={schema} />}
        </div>
      </div>
    </div>
  );
}
