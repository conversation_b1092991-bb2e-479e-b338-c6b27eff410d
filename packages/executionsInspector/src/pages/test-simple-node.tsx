// Simplified NodeParameterEvaluator without Select component
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Settings, Play, AlertCircle } from 'lucide-react';
import { evaluateNodeParameters } from '../lib/jsonLogicUtils';

interface NodeParameterEvaluatorProps {
  schema: any;
}

export function SimpleNodeParameterEvaluator({ schema }: NodeParameterEvaluatorProps) {
  const [selectedNodeId, setSelectedNodeId] = useState('');
  const [inputData, setInputData] = useState('{\n  "input": {},\n  "trigger": {}\n}');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Simple Node Parameter Evaluator</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Input Data (JSON)</label>
          <Textarea
            value={inputData}
            onChange={(e) => setInputData(e.target.value)}
            placeholder="Enter input data..."
            className="min-h-[100px] font-mono text-xs"
          />
        </div>
        
        <Button size="sm">
          <Play className="h-4 w-4 mr-2" />
          Evaluate Parameters (Simplified)
        </Button>

        {result && (
          <div className="p-3 bg-muted rounded-md">
            <h4 className="text-sm font-medium mb-2">Evaluation Result:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        {error && (
          <div className="flex items-start space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5" />
            <div className="text-sm text-red-700 dark:text-red-300">{error}</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function TestSimpleNodePage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <h1>Testing Simple NodeParameterEvaluator (No Select)</h1>
        <SimpleNodeParameterEvaluator schema={null} />
      </div>
    </div>
  );
}
