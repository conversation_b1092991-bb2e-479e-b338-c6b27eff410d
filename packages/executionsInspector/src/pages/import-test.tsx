// Test each import individually to identify the undefined component
import { useState } from 'react';

// Test imports one by one
let importResults: any = {};

try {
  const lucideIcons = require('lucide-react');
  importResults.lucideReact = { Activity: lucideIcons.Activity, ArrowLeft: lucideIcons.ArrowLeft };
  console.log('✅ lucide-react imported successfully');
} catch (e) {
  importResults.lucideReact = { error: e };
  console.error('❌ lucide-react failed:', e);
}

try {
  const router = require('next/router');
  importResults.nextRouter = router.useRouter;
  console.log('✅ next/router imported successfully');
} catch (e) {
  importResults.nextRouter = { error: e };
  console.error('❌ next/router failed:', e);
}

try {
  const themeToggle = require('@/components/ThemeToggle');
  importResults.themeToggle = themeToggle.ThemeToggle;
  console.log('✅ ThemeToggle imported:', themeToggle.ThemeToggle);
} catch (e) {
  importResults.themeToggle = { error: e };
  console.error('❌ ThemeToggle failed:', e);
}

try {
  const schemaInput = require('@/components/SchemaInput');
  importResults.schemaInput = schemaInput.SchemaInput;
  console.log('✅ SchemaInput imported:', schemaInput.SchemaInput);
} catch (e) {
  importResults.schemaInput = { error: e };
  console.error('❌ SchemaInput failed:', e);
}

try {
  const nodeEval = require('@/components/NodeParameterEvaluator');
  importResults.nodeParameterEvaluator = nodeEval.NodeParameterEvaluator;
  console.log('✅ NodeParameterEvaluator imported:', nodeEval.NodeParameterEvaluator);
} catch (e) {
  importResults.nodeParameterEvaluator = { error: e };
  console.error('❌ NodeParameterEvaluator failed:', e);
}

try {
  const conditionMatcher = require('@/components/ConditionMatcher');
  importResults.conditionMatcher = conditionMatcher.ConditionMatcher;
  console.log('✅ ConditionMatcher imported:', conditionMatcher.ConditionMatcher);
} catch (e) {
  importResults.conditionMatcher = { error: e };
  console.error('❌ ConditionMatcher failed:', e);
}

export default function ImportTestPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-4">Import Test Results</h1>
        <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-xs overflow-auto">
          {JSON.stringify(importResults, null, 2)}
        </pre>
        <p className="mt-4 text-sm text-muted-foreground">
          Check the console for detailed import results. Any undefined components will be highlighted.
        </p>
      </div>
    </div>
  );
}
