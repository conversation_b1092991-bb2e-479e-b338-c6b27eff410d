import React from 'react';
import { Mail, FileText, MoreHorizontal, ExternalLink, Paperclip } from 'lucide-react';
import { GmailDraftOutput, UrlAccessibleFile } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GmailDraftOutputDisplayProps = {
  output: GmailDraftOutput;
  actionParameters?: Record<string, any>;
};

// Helper function to extract filename from URL
function extractFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || 'attachment';
    return decodeURIComponent(filename);
  } catch {
    return 'attachment';
  }
}

// Component for displaying file attachments
interface GmailAttachmentsProps {
  attachments: UrlAccessibleFile[];
}

function GmailAttachments({ attachments }: GmailAttachmentsProps) {
  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 space-y-2">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Attachments ({attachments.length})
      </div>
      {attachments.map((attachment, index) => {
        const filename = extractFilenameFromUrl(attachment.url);

        const menuItems: ContextMenuItem[] = [
          {
            label: 'View File',
            onClick: () => window.open(attachment.url, '_blank'),
            icon: <ExternalLink className="w-4 h-4 mr-2" />,
          },
        ];

        return (
          <div
            key={index}
            className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            {/* File icon */}
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <Paperclip className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>

            {/* File info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {filename}
                </p>
                <ContextMenu items={menuItems}>
                  <button className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 ml-2">
                    <ExternalLink className="w-4 h-4" />
                  </button>
                </ContextMenu>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

/**
 * Renders a rich display of a Gmail draft creation result
 */
function GmailDraftOutputDisplay({ output, actionParameters }: GmailDraftOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No draft data available</p>
      </div>
    );
  }

  // Extract draft info from action parameters
  const recipient = actionParameters?.recipient;
  const subject = actionParameters?.subject;
  const attachments = actionParameters?.attachments as UrlAccessibleFile[] | undefined;

  const menuItems: ContextMenuItem[] = [
    {
      label: 'View in Gmail Drafts',
      href: 'https://mail.google.com/mail/u/0/#drafts',
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Gmail Draft Created
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5 space-y-4">
        {/* Draft details */}
        <div className="space-y-3">
          {subject && (
            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Subject
              </div>
              <div className="text-sm text-gray-900 dark:text-white">
                {subject}
              </div>
            </div>
          )}

          {recipient && (
            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                To
              </div>
              <div className="text-sm text-gray-900 dark:text-white">
                {recipient}
              </div>
            </div>
          )}
        </div>

        {/* Body preview if available */}
        {actionParameters?.body && (
          <div className="p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Body Preview
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line line-clamp-3">
              {actionParameters.body}
            </div>
          </div>
        )}

        {/* Attachments */}
        {attachments && attachments.length > 0 && (
          <GmailAttachments attachments={attachments} />
        )}

      </div>
    </div>
    </ContextMenu>
  );
}

export { GmailDraftOutputDisplay };
